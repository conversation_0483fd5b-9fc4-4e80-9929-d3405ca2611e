{"permissions": {"allow": ["mcp__xcodebuild__discover_projs", "mcp__xcodebuild__list_schems_proj", "mcp__apple-doc-mcp__get_documentation", "mcp__apple-doc-mcp__search_symbols", "mcp__apple-doc-mcp__list_technologies", "mcp__xcodebuild__build_sim_name_proj", "mcp__xcodebuild__show_build_set_proj", "mcp__xcodebuild__list_sims", "mcp__xcodebuild__boot_sim", "mcp__xcodebuild__open_sim", "mcp__xcodebuild__build_run_sim_id_proj", "mcp__xcodebuild__screenshot", "mcp__xcodebuild__build_run_sim_name_ws", "mcp__xcodebuild__tap", "mcp__xcodebuild__describe_ui", "mcp__framelink-figma__get_figma_data", "WebFetch(domain:www.figma.com)", "mcp__xcodebuild__get_sim_app_path_name_proj", "mcp__xcodebuild__get_app_bundle_id", "mcp__xcodebuild__install_app_sim", "mcp__xcodebuild__launch_app_sim", "mcp__xcodebuild__build_run_sim_name_proj", "mcp__xcodebuild__stop_app_sim", "mcp__xcodebuild__test_sim_name_proj", "mcp__xcodebuild__get_sim_app_path_id_proj", "mcp__xcodebuild__build_sim_id_proj", "mcp__xcodebuild__launch_app_logs_sim", "mcp__xcodebuild__stop_sim_log_cap", "mcp__xcodebuild__list_devices", "mcp__xcodebuild__build_run_mac_proj", "Bash(mcp__xcodebuild__list_schems_proj:*)", "Bash(xcodebuild:*)", "mcp__xcodebuild__start_sim_log_cap", "mcp__xcodebuild__clean_proj", "mcp__xcodebuild__swipe", "mcp__xcodebuild__type_text", "Bash(git checkout:*)", "Bash(xcrun simctl boot:*)", "Bash(xcrun simctl spawn:*)", "Bash(xcrun simctl shutdown:*)", "Bash(perl:*)", "<PERSON><PERSON>(swiftc:*)"], "deny": []}}