//
//  NotificationManagerTests.swift
//  LightningHabitTests
//
//  Unit tests for NotificationManager functionality
//

import XCTest
import UserNotifications
@testable import app

final class NotificationManagerTests: XCTestCase {
    var notificationManager: NotificationManager!
    var testHabit: Habit!
    
    override func setUpWithError() throws {
        notificationManager = NotificationManager.shared
        testHabit = Habit(
            name: "Test Habit",
            icon: "star.fill",
            reminderEnabled: true,
            reminderTime: Calendar.current.date(from: DateComponents(hour: 9, minute: 0))
        )
    }
    
    override func tearDownWithError() throws {
        notificationManager = nil
        testHabit = nil
    }
    
    // MARK: - Notification Settings Tests
    func testDefaultNotificationSettings() throws {
        let settings = notificationManager.notificationSettings
        
        XCTAssertTrue(settings.suppressWhenActive)
        XCTAssertTrue(settings.smartReminders)
        XCTAssertFalse(settings.useGenericContent)
        XCTAssertTrue(settings.showProgressInNotifications)
        XCTAssertNotNil(settings.defaultTime)
    }
    
    func testGenericContentSetting() throws {
        // Test non-generic content (default)
        notificationManager.notificationSettings.useGenericContent = false
        
        // Note: Testing actual notification content would require mock UNUserNotificationCenter
        // This tests the setting toggle functionality
        XCTAssertFalse(notificationManager.notificationSettings.useGenericContent)
        
        // Test generic content
        notificationManager.notificationSettings.useGenericContent = true
        XCTAssertTrue(notificationManager.notificationSettings.useGenericContent)
    }
    
    func testSuppressionWhenActiveSetting() throws {
        // Test default setting
        XCTAssertTrue(notificationManager.notificationSettings.suppressWhenActive)
        
        // Test toggle
        notificationManager.notificationSettings.suppressWhenActive = false
        XCTAssertFalse(notificationManager.notificationSettings.suppressWhenActive)
    }
    
    // MARK: - Habit Notification Management Tests
    func testHabitNotificationEnabled() throws {
        let habitId = testHabit.id
        
        // Initially not enabled
        XCTAssertFalse(notificationManager.notificationSettings.enabledForHabits.contains(habitId))
        
        // Enable notifications for habit
        notificationManager.notificationSettings.enabledForHabits.insert(habitId)
        XCTAssertTrue(notificationManager.notificationSettings.enabledForHabits.contains(habitId))
        
        // Disable notifications for habit
        notificationManager.notificationSettings.enabledForHabits.remove(habitId)
        XCTAssertFalse(notificationManager.notificationSettings.enabledForHabits.contains(habitId))
    }
    
    func testDefaultTimeManagement() throws {
        let newTime = Calendar.current.date(from: DateComponents(hour: 8, minute: 30))!
        
        // Update default time
        notificationManager.notificationSettings.defaultTime = newTime
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: notificationManager.notificationSettings.defaultTime)
        
        XCTAssertEqual(components.hour, 8)
        XCTAssertEqual(components.minute, 30)
    }
    
    // MARK: - Notification Content Tests
    func testNotificationIdentifierGeneration() throws {
        let habitId = testHabit.id
        let expectedIdentifier = "habit_reminder_\(habitId.uuidString)"
        
        // This would typically be tested through the actual scheduling method
        // For now, we test the pattern
        XCTAssertTrue(expectedIdentifier.starts(with: "habit_reminder_"))
        XCTAssertTrue(expectedIdentifier.contains(habitId.uuidString))
    }
    
    // MARK: - Authorization Tests
    func testInitialAuthorizationState() throws {
        // NotificationManager should initialize with unknown authorization state
        // until checkAuthorizationStatus() completes
        // This test verifies the initial state setup
        
        let freshManager = NotificationManager()
        
        // The authorization state should be determined asynchronously
        // We can't easily test this without mocking UNUserNotificationCenter
        XCTAssertNotNil(freshManager.notificationSettings)
    }
    
    // MARK: - Performance Tests
    func testNotificationSettingsPerformance() throws {
        // Test rapid setting changes don't cause performance issues
        measure {
            for i in 0..<1000 {
                notificationManager.notificationSettings.useGenericContent = (i % 2 == 0)
                notificationManager.notificationSettings.suppressWhenActive = (i % 3 == 0)
            }
        }
    }
    
    func testHabitSetManipulationPerformance() throws {
        // Test performance of large habit sets
        let habitIds = (0..<1000).map { _ in UUID() }
        
        measure {
            for id in habitIds {
                notificationManager.notificationSettings.enabledForHabits.insert(id)
            }
            
            for id in habitIds {
                notificationManager.notificationSettings.enabledForHabits.remove(id)
            }
        }
    }
    
    // MARK: - Edge Cases
    func testEmptyHabitName() throws {
        let emptyNameHabit = Habit(name: "", icon: "star.fill")
        
        // The notification system should handle empty names gracefully
        // This tests that we don't crash with edge case data
        XCTAssertEqual(emptyNameHabit.name, "")
        XCTAssertNotNil(emptyNameHabit.id)
    }
    
    func testNilReminderTime() throws {
        let noReminderHabit = Habit(
            name: "No Reminder Habit",
            icon: "star.fill",
            reminderEnabled: false,
            reminderTime: nil
        )
        
        XCTAssertNil(noReminderHabit.reminderTime)
        XCTAssertFalse(noReminderHabit.reminderEnabled)
    }
}

// MARK: - Mock Classes (for future use)
/*
class MockUNUserNotificationCenter: UNUserNotificationCenter {
    var mockAuthorizationStatus: UNAuthorizationStatus = .notDetermined
    var scheduledRequests: [UNNotificationRequest] = []
    
    override func getNotificationSettings(completionHandler: @escaping (UNNotificationSettings) -> Void) {
        // Mock implementation
    }
    
    override func requestAuthorization(options: UNAuthorizationOptions) async throws -> Bool {
        // Mock implementation
        return true
    }
    
    override func add(_ request: UNNotificationRequest) async throws {
        scheduledRequests.append(request)
    }
}
*/