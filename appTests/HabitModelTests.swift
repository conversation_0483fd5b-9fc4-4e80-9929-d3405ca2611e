//
//  HabitModelTests.swift
//  LightningHabitTests
//
//  Unit tests for Habit model business logic
//

import XCTest
import SwiftData
@testable import app

final class HabitModelTests: XCTestCase {
    var modelContext: ModelContext!
    var testHabit: Habit!
    
    override func setUpWithError() throws {
        // Create in-memory model container for testing
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: Habit.self, HabitLog.self, configurations: config)
        modelContext = ModelContext(container)
        
        // Create test habit
        testHabit = Habit(name: "Test Habit", icon: "star.fill")
        modelContext.insert(testHabit)
        try modelContext.save()
    }
    
    override func tearDownWithError() throws {
        modelContext = nil
        testHabit = nil
    }
    
    // MARK: - Habit Creation Tests
    func testHabitInitialization() throws {
        let habit = Habit(
            name: "Morning Run",
            icon: "figure.run",
            order: 5,
            isActive: true,
            reminderEnabled: true
        )
        
        XCTAssertEqual(habit.name, "Morning Run")
        XCTAssertEqual(habit.icon, "figure.run")
        XCTAssertEqual(habit.order, 5)
        XCTAssertTrue(habit.isActive)
        XCTAssertTrue(habit.reminderEnabled)
        XCTAssertNotNil(habit.id)
        XCTAssertNotNil(habit.createdAt)
        XCTAssertNotNil(habit.updatedAt)
    }
    
    // MARK: - Streak Calculation Tests
    func testCurrentStreakWithNoLogs() throws {
        XCTAssertEqual(testHabit.currentStreak, 0)
    }
    
    func testCurrentStreakWithConsecutiveDays() throws {
        let calendar = Calendar.current
        let today = Date()
        
        // Add completed logs for last 3 days
        for i in 0..<3 {
            let date = calendar.date(byAdding: .day, value: -i, to: today)!
            let log = HabitLog(habit: testHabit, status: .completed, date: date)
            testHabit.logs.append(log)
        }
        
        XCTAssertEqual(testHabit.currentStreak, 3)
    }
    
    func testCurrentStreakWithSkippedDay() throws {
        let calendar = Calendar.current
        let today = Date()
        
        // Add completed log for today
        let todayLog = HabitLog(habit: testHabit, status: .completed, date: today)
        testHabit.logs.append(todayLog)
        
        // Add completed log for 2 days ago (skip yesterday)
        let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: today)!
        let oldLog = HabitLog(habit: testHabit, status: .completed, date: twoDaysAgo)
        testHabit.logs.append(oldLog)
        
        // Should still count as streak of 2 due to allowed skip days
        XCTAssertEqual(testHabit.currentStreak, 2)
    }
    
    func testLongestStreakCalculation() throws {
        let calendar = Calendar.current
        let today = Date()
        
        // Create a pattern: 5 days completed, 2 days gap, 3 days completed
        for i in 0..<5 {
            let date = calendar.date(byAdding: .day, value: -(i + 10), to: today)!
            let log = HabitLog(habit: testHabit, status: .completed, date: date)
            testHabit.logs.append(log)
        }
        
        for i in 0..<3 {
            let date = calendar.date(byAdding: .day, value: -i, to: today)!
            let log = HabitLog(habit: testHabit, status: .completed, date: date)
            testHabit.logs.append(log)
        }
        
        // Longest streak should be 5
        XCTAssertEqual(testHabit.longestStreak, 5)
    }
    
    func testCompletionRateCalculation() throws {
        // Add 3 completed and 2 skipped logs
        for i in 0..<3 {
            let log = HabitLog(habit: testHabit, status: .completed)
            testHabit.logs.append(log)
        }
        
        for i in 0..<2 {
            let log = HabitLog(habit: testHabit, status: .skipped)
            testHabit.logs.append(log)
        }
        
        // Completion rate should be 0.6 (3/5)
        XCTAssertEqual(testHabit.completionRate, 0.6, accuracy: 0.001)
    }
    
    // MARK: - Today's Log Tests
    func testTodayLogWithNoLogs() throws {
        XCTAssertNil(testHabit.todayLog)
        XCTAssertFalse(testHabit.isCompletedToday)
    }
    
    func testTodayLogWithTodaysLog() throws {
        let todayLog = HabitLog(habit: testHabit, status: .completed, date: Date())
        testHabit.logs.append(todayLog)
        
        XCTAssertNotNil(testHabit.todayLog)
        XCTAssertTrue(testHabit.isCompletedToday)
    }
    
    // MARK: - Action Methods Tests
    func testMarkAsCompleted() throws {
        testHabit.markAsCompleted()
        
        XCTAssertTrue(testHabit.isCompletedToday)
        XCTAssertEqual(testHabit.logs.count, 1)
        XCTAssertEqual(testHabit.logs.first?.status, .completed)
        XCTAssertNotNil(testHabit.logs.first?.completedAt)
    }
    
    func testMarkAsSkipped() throws {
        testHabit.markAsSkipped()
        
        XCTAssertFalse(testHabit.isCompletedToday)
        XCTAssertEqual(testHabit.logs.count, 1)
        XCTAssertEqual(testHabit.logs.first?.status, .skipped)
    }
    
    func testResetToday() throws {
        // First mark as completed
        testHabit.markAsCompleted()
        XCTAssertTrue(testHabit.isCompletedToday)
        
        // Then reset
        testHabit.resetToday()
        XCTAssertFalse(testHabit.isCompletedToday)
        XCTAssertEqual(testHabit.todayLog?.status, .pending)
        XCTAssertNil(testHabit.todayLog?.completedAt)
    }
    
    // MARK: - Cache Performance Tests
    func testCacheInvalidation() throws {
        // First call should calculate and cache
        let initialStreak = testHabit.currentStreak
        
        // Add a log
        let log = HabitLog(habit: testHabit, status: .completed)
        testHabit.logs.append(log)
        testHabit.markAsCompleted() // This should invalidate cache
        
        // Second call should return updated value
        let updatedStreak = testHabit.currentStreak
        XCTAssertEqual(updatedStreak, initialStreak + 1)
    }
    
    func testCachePerformance() throws {
        // Add many logs to test performance
        for i in 0..<100 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: Date())!
            let log = HabitLog(habit: testHabit, status: .completed, date: date)
            testHabit.logs.append(log)
        }
        
        // Measure cache performance
        measure {
            _ = testHabit.currentStreak
            _ = testHabit.longestStreak
            _ = testHabit.completionRate
        }
    }
}