# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# ⚡ SwiftHabits – Product Functionality & Mechanism Overview

A fast, minimalist, swipe-based habit tracker with core simplicity and premium power features.

---

## Project Overview

**Project:** SwiftHabits  
**Target Audience:** iOS users seeking a minimalist, ultra-fast, joyfully simple habit-tracking experience  
**Platform:** iOS 17+ (iPhone optimized, iPad compatible)  
**Architecture:** Swift + SwiftUI + SwiftData with MVVM pattern

---

## 🚀 Core Functionality & Mechanisms

### 1. Launch → Instant Habit Card Loading
- **Mechanism**: On app launch, the most relevant incomplete habit is fetched from SwiftData and rendered immediately
- **Speed Target**: Habit card fully usable in < 0.3 seconds
- **Implementation**: Uses `@Query` with predicates to filter active, incomplete habits

### 2. 🔄 Swipe Gesture Interaction
- **Right swipe = Mark as Done**, **Left swipe = Skip/Miss** (user-configurable)
- Smooth rotation and slide-out animation, color-coded overlays (green/red)
- **Undo button** appears briefly after each swipe, enabling quick correction
- **Tap alternative**: Checkbox buttons for accessibility
- **Implementation**: `DragGesture` with haptic feedback via `UIImpactFeedbackGenerator`

### 3. 📅 Streak Logic & Local Progress
- Each interaction is logged instantly in SwiftData with timestamp and result
- Optional "Streak Protection" allows skipping without breaking streak (1 day default)
- Handles timezone offsets and custom day windows
- **Implementation**: Computed properties in `Habit` model with configurable skip-day logic

### 4. 📢 Smart Local Notifications
- Scheduled reminders using `UNUserNotificationCenter`, customized per habit
- If habit was marked recently or user is currently active, suppress notification
- Avoids cloud dependencies—offline-reliable and energy-efficient

### 5. ☁️ Data Sync & Export
- SwiftData used locally for instant access and state preservation
- Seamless iCloud sync via `cloudKitDatabase: .automatic`
- Export options: JSON and CSV via native share sheet

### 6. 🎨 UI & Animations
- **Liquid Glass Design**: Ultra-thin materials with gradient overlays
- **Animations**: 60fps spring animations, swipe rotations, completion confetti
- **Visual Elements**: Animated background blur circles, glass borders, depth effects
- **Dark Mode**: Optimized for dark appearance with high contrast

### 7. 🎓 Onboarding & Interaction Hints
- First-time overlay: "Swipe right = done", "Swipe left = skip"
- After successful first interaction, overlay disappears forever
- Subtle visual cues on card edges until habits are mastered

### 8. ⚙️ Architecture & Performance
- **Tech Stack**: Swift 5.9+ / SwiftUI / SwiftData
- **Architecture**: Simplified MVVM with direct SwiftData integration
- **Performance**: < 200ms cold boot, 60fps animations
- **Data Models**: `Habit` and `HabitLog` with automatic iCloud sync

---

## 📁 Project Structure

```
app/
├── Models/
│   ├── Habit.swift          # Main habit model with streak logic
│   └── HabitLog.swift       # Daily habit completion tracking
├── Views/
│   ├── MainView.swift       # Primary screen with liquid glass design
│   ├── HabitCard.swift      # Tinder-style swipeable habit card
│   └── CompletionView.swift # Celebration screen with confetti
├── Resources/
│   └── Assets.xcassets/     # App icons and visual assets
├── appApp.swift             # App entry point with SwiftData setup
├── appTests/                # Unit tests (Swift Testing framework)
└── appUITests/              # UI automation tests
```

---

## 🛠 Development Guidelines

### Building the App
```bash
# Build for iOS Simulator
xcodebuild -project app.xcodeproj -scheme app -destination 'name=iPhone 16' build

# Run tests
xcodebuild test -project app.xcodeproj -scheme app -destination 'name=iPhone 16'
```

### MCP Tools Usage

**Xcodebuild Tools:**
- `mcp__xcodebuild__build_sim_name_proj` - Build for simulator
- `mcp__xcodebuild__test_sim_name_proj` - Run tests
- `mcp__xcodebuild__launch_app_sim` - Launch app in simulator
- `mcp__xcodebuild__describe_ui` - Get UI hierarchy for automation

**Apple Documentation Tools:**
- `mcp__apple-doc-mcp__get_documentation` - Get framework docs
- `mcp__apple-doc-mcp__search_symbols` - Search SwiftUI/iOS symbols

### Key Implementation Details

1. **SwiftData Integration**
   - Models use `@Model` macro for automatic persistence
   - `ModelContainer` configured with iCloud sync
   - Relationships managed with `@Relationship` and delete rules

2. **Liquid Glass Design**
   - `.ultraThinMaterial` for translucent effects
   - Gradient overlays with `LinearGradient`
   - Animated blur circles in background for depth
   - Glass borders with gradient strokes

3. **Swipe Mechanics**
   - `DragGesture` with threshold detection
   - Rotation effects based on drag distance
   - Color-coded overlays fade in during swipe
   - Haptic feedback at interaction points

4. **Performance Optimizations**
   - Lazy loading with SwiftData queries
   - Minimal view hierarchy
   - Cached computations for streaks
   - Direct model updates without service layers

---

## 🎯 Success Metrics & Requirements

- **Speed**: App launch to habit log < 2 seconds
- **Reliability**: 99.9% crash-free sessions
- **Performance**: Consistent 60fps animations
- **Accessibility**: Full VoiceOver support with tap alternatives
- **Data**: Offline-first with seamless iCloud sync

---

## 🔏 Premium Features (Future)

### Habit Templates & Suggestions
- 30+ curated habit templates
- One-tap import to habit list
- StoreKit integration for subscriptions
- Free 7-day trial with monthly/yearly options

---

## 📝 Current Development Status

### ✅ Completed
- Project setup and architecture
- Liquid glass UI design system
- SwiftData models with iCloud sync
- Tinder-style swipe interactions
- Habit tracking with streak logic
- Completion celebrations with confetti
- Real-time statistics display

### 🚧 In Progress
- Smart notification system
- Settings and preferences UI
- Data export functionality
- App metadata configuration

### 📋 Planned
- Widgets and complications
- Advanced habit templates
- Performance profiling
- App Store preparation

---

## 🧠 Claude Code Integration Notes

When working on this project:
1. Use MCP tools for building and testing
2. Reference Apple docs for SwiftUI/SwiftData APIs
3. Maintain 60fps performance target
4. Follow liquid glass design patterns
5. Keep interactions under 2 seconds
6. Test with VoiceOver for accessibility

---

**SwiftHabits** is designed for people who want no distractions—just open the app, swipe, and move on with their day.