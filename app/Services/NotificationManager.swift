//
//  NotificationManager.swift
//  SwiftHabits
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import Foundation
import UserNotifications
import SwiftUI

@MainActor
class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    @Published var notificationSettings: NotificationSettings = NotificationSettings()
    
    private let notificationCenter = UNUserNotificationCenter.current()
    
    struct NotificationSettings {
        var enabledForHabits: Set<UUID> = []
        var defaultTime: Date = Calendar.current.date(from: DateComponents(hour: 9, minute: 0)) ?? Date()
        var suppressWhenActive = true
        var smartReminders = true
        var useGenericContent = false // Privacy setting to hide habit names
        var showProgressInNotifications = true
    }
    
    init() {
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    func requestAuthorization() async {
        do {
            let granted = try await notificationCenter.requestAuthorization(options: [.alert, .sound, .badge])
            await MainActor.run {
                self.isAuthorized = granted
            }
            
            if granted {
                await scheduleDefaultNotifications()
            }
        } catch {
            // Failed to request notification authorization
        }
    }
    
    func checkAuthorizationStatus() {
        notificationCenter.getNotificationSettings { settings in
            Task { @MainActor in
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Scheduling
    func scheduleNotification(for habit: Habit, at time: Date? = nil) async {
        guard isAuthorized else { return }
        
        // Remove existing notifications for this habit
        await cancelNotifications(for: habit)
        
        // Don't schedule if notifications are disabled for this habit
        guard notificationSettings.enabledForHabits.contains(habit.id) else { return }
        
        // Use provided time or default time
        let scheduledTime = time ?? notificationSettings.defaultTime
        
        // Create notification content
        let content = UNMutableNotificationContent()
        
        if notificationSettings.useGenericContent {
            content.title = String(localized: "habit_reminder")
            content.body = "Time to work on your daily habits. Open the app to continue your progress."
        } else {
            content.title = "Time for: \(habit.name)"
            content.body = "Tap to mark as complete or open the app to log your habit."
        }
        
        content.sound = .default
        content.categoryIdentifier = "HABIT_REMINDER"
        content.userInfo = ["habitId": habit.id.uuidString]
        
        // Create daily trigger
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: scheduledTime)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
        
        // Create request
        let request = UNNotificationRequest(
            identifier: "habit-\(habit.id.uuidString)",
            content: content,
            trigger: trigger
        )
        
        // Schedule notification
        do {
            try await notificationCenter.add(request)
            // Successfully scheduled notification
        } catch {
            // Failed to schedule notification
        }
    }
    
    func scheduleDefaultNotifications() async {
        // This will be called when habits are loaded
        // For now, we'll just set up the notification categories
        await setupNotificationCategories()
    }
    
    func cancelNotifications(for habit: Habit) async {
        notificationCenter.removePendingNotificationRequests(withIdentifiers: ["habit-\(habit.id.uuidString)"])
    }
    
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    // MARK: - Smart Features
    func suppressNotificationIfActive() async -> Bool {
        guard notificationSettings.suppressWhenActive else { return false }
        
        // Check if app was used recently (within last 30 minutes)
        // This would be tracked by checking last habit log time
        // For now, we'll return false
        return false
    }
    
    // MARK: - Notification Actions
    private func setupNotificationCategories() async {
        let completeAction = UNNotificationAction(
            identifier: "COMPLETE_HABIT",
            title: "Mark as Done",
            options: [.foreground]
        )
        
        let skipAction = UNNotificationAction(
            identifier: "SKIP_HABIT",
            title: String(localized: "skip_today"),
            options: []
        )
        
        let category = UNNotificationCategory(
            identifier: "HABIT_REMINDER",
            actions: [completeAction, skipAction],
            intentIdentifiers: [],
            options: []
        )
        
        notificationCenter.setNotificationCategories([category])
    }
    
    // MARK: - Settings Management
    func enableNotifications(for habit: Habit) {
        notificationSettings.enabledForHabits.insert(habit.id)
        Task {
            await scheduleNotification(for: habit)
        }
    }
    
    func disableNotifications(for habit: Habit) {
        notificationSettings.enabledForHabits.remove(habit.id)
        Task {
            await cancelNotifications(for: habit)
        }
    }
    
    func updateDefaultTime(_ time: Date) {
        notificationSettings.defaultTime = time
        // Reschedule all notifications with new time
        Task {
            // This would iterate through all habits and reschedule
            // Implementation depends on having access to habits
        }
    }
}