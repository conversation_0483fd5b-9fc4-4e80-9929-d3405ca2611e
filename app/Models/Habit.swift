//
//  Habit.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import Foundation
import SwiftData

@Model
final class Habit {
    var id: UUID
    var name: String
    var icon: String
    var order: Int
    var isActive: Bool
    var createdAt: Date
    var updatedAt: Date
    
    // Reminder properties
    var reminderEnabled: Bool = false
    var reminderTime: Date?
    var notificationId: String?
    
    // Cache for expensive calculations
    @Transient private var _cachedCurrentStreak: Int?
    @Transient private var _cachedLongestStreak: Int?
    @Transient private var _cachedCompletionRate: Double?
    @Transient private var _cacheLastUpdated: Date?
    
    // Relationships
    @Relationship(deleteRule: .cascade, inverse: \HabitLog.habit)
    var logs: [HabitLog] = []
    
    // Cached computed properties
    var currentStreak: Int {
        if let cached = _cachedCurrentStreak,
           let lastUpdated = _cacheLastUpdated,
           Date().timeIntervalSince(lastUpdated) < 300 { // 5-minute cache
            return cached
        }
        
        let streak = calculateCurrentStreak()
        _cachedCurrentStreak = streak
        _cacheLastUpdated = Date()
        return streak
    }
    
    var longestStreak: Int {
        if let cached = _cachedLongestStreak,
           let lastUpdated = _cacheLastUpdated,
           Date().timeIntervalSince(lastUpdated) < 300 {
            return cached
        }
        
        let streak = calculateLongestStreak()
        _cachedLongestStreak = streak
        _cacheLastUpdated = Date()
        return streak
    }
    
    var completionRate: Double {
        if let cached = _cachedCompletionRate,
           let lastUpdated = _cacheLastUpdated,
           Date().timeIntervalSince(lastUpdated) < 300 {
            return cached
        }
        
        let rate = calculateCompletionRate()
        _cachedCompletionRate = rate
        _cacheLastUpdated = Date()
        return rate
    }
    
    var todayLog: HabitLog? {
        logs.first { Calendar.current.isDateInToday($0.date) }
    }
    
    var isCompletedToday: Bool {
        todayLog?.status == .completed
    }
    
    init(
        name: String,
        icon: String,
        order: Int = 0,
        isActive: Bool = true,
        reminderEnabled: Bool = false,
        reminderTime: Date? = nil
    ) {
        self.id = UUID()
        self.name = name
        self.icon = icon
        self.order = order
        self.isActive = isActive
        self.createdAt = Date()
        self.updatedAt = Date()
        self.reminderEnabled = reminderEnabled
        self.reminderTime = reminderTime
        self.notificationId = nil
        // logs initializes as empty array by default
    }
    
    // MARK: - Methods
    func markAsCompleted() {
        if let todayLog = todayLog {
            todayLog.status = .completed
            todayLog.completedAt = Date()
        } else {
            let newLog = HabitLog(habit: self, status: .completed)
            logs.append(newLog)
        }
        updatedAt = Date()
        invalidateCache()
    }
    
    func markAsSkipped() {
        if let todayLog = todayLog {
            todayLog.status = .skipped
            todayLog.completedAt = Date()
        } else {
            let newLog = HabitLog(habit: self, status: .skipped)
            logs.append(newLog)
        }
        updatedAt = Date()
        invalidateCache()
    }
    
    func resetToday() {
        if let todayLog = todayLog {
            todayLog.status = .pending
            todayLog.completedAt = nil
        }
        updatedAt = Date()
        invalidateCache()
    }
    
    // MARK: - Cache Management
    private func invalidateCache() {
        _cachedCurrentStreak = nil
        _cachedLongestStreak = nil
        _cachedCompletionRate = nil
        _cacheLastUpdated = nil
    }
    
    // MARK: - Streak Calculations
    private func calculateCurrentStreak() -> Int {
        guard !logs.isEmpty else { return 0 }
        
        let calendar = Calendar.current
        let sortedLogs = logs
            .filter { $0.status == .completed }
            .sorted { $0.date > $1.date }
        
        guard !sortedLogs.isEmpty else { return 0 }
        
        var streak = 0
        var currentDate = Date()
        
        // Check if today is completed
        if let firstLogDate = sortedLogs.first?.date,
           !calendar.isDateInToday(firstLogDate) {
            // If today is not completed, start from yesterday
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        }
        
        for log in sortedLogs {
            if calendar.isDate(log.date, inSameDayAs: currentDate) {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else if log.date < currentDate {
                // Check for skip days
                let daysBetween = calendar.dateComponents([.day], from: log.date, to: currentDate).day ?? 0
                if daysBetween <= getAllowedSkipDays(between: log.date, and: currentDate) {
                    streak += 1
                    currentDate = calendar.date(byAdding: .day, value: -daysBetween - 1, to: currentDate)!
                } else {
                    break
                }
            }
        }
        
        return streak
    }
    
    private func calculateLongestStreak() -> Int {
        guard !logs.isEmpty else { return 0 }
        
        let calendar = Calendar.current
        let sortedLogs = logs
            .filter { $0.status == .completed }
            .sorted { $0.date < $1.date }
        
        var longestStreak = 0
        var currentStreak = 0
        var lastDate: Date?
        
        for log in sortedLogs {
            if let lastDate = lastDate {
                let daysBetween = calendar.dateComponents([.day], from: lastDate, to: log.date).day ?? 0
                if daysBetween <= 1 + getAllowedSkipDays(between: lastDate, and: log.date) {
                    currentStreak += 1
                } else {
                    longestStreak = max(longestStreak, currentStreak)
                    currentStreak = 1
                }
            } else {
                currentStreak = 1
            }
            lastDate = log.date
        }
        
        return max(longestStreak, currentStreak)
    }
    
    private func calculateCompletionRate() -> Double {
        guard !logs.isEmpty else { return 0.0 }
        
        let completedCount = logs.filter { $0.status == .completed }.count
        let totalDays = logs.count
        
        return Double(completedCount) / Double(totalDays)
    }
    
    private func getAllowedSkipDays(between startDate: Date, and endDate: Date) -> Int {
        // TODO: Make this configurable in settings
        // For now, allow 1 skip day
        return 1
    }
}

// MARK: - Conformances
extension Habit: Hashable {
    static func == (lhs: Habit, rhs: Habit) -> Bool {
        lhs.id == rhs.id
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}