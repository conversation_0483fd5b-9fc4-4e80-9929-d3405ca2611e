//
//  HabitLog.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import Foundation
import SwiftData

enum HabitStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case completed = "completed"
    case skipped = "skipped"
}

@Model
final class HabitLog {
    var id: UUID
    var date: Date
    var status: HabitStatus
    var completedAt: Date?
    var note: String?
    
    // Relationship
    var habit: Habit?
    
    init(
        habit: Habit,
        date: Date = Date(),
        status: HabitStatus = .pending,
        note: String? = nil
    ) {
        self.id = UUID()
        self.habit = habit
        self.date = Calendar.current.startOfDay(for: date)
        self.status = status
        self.completedAt = status == .pending ? nil : Date()
        self.note = note
    }
    
    // MARK: - Computed Properties
    var isToday: Bool {
        Calendar.current.isDateInToday(date)
    }
    
    var dayOfWeek: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        return formatter.string(from: date)
    }
    
    var formattedDate: String {
        let formatter = DateFormatt<PERSON>()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
    
    var completionTime: String? {
        guard let completedAt = completedAt else { return nil }
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: completedAt)
    }
}