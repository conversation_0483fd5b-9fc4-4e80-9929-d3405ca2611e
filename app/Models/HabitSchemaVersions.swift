//
//  HabitSchemaVersions.swift
//  LightningHabit
//
//  Migration infrastructure for SwiftData schema evolution
//

import Foundation
import SwiftData

// MARK: - Schema Version 1.0 (Current)
enum HabitSchemaV1: VersionedSchema {
    static var versionIdentifier = Schema.Version(1, 0, 0)
    
    static var models: [any PersistentModel.Type] {
        [Habit.self, HabitLog.self]
    }
    
    @Model
    final class Habit {
        var id: UUID
        var name: String
        var icon: String
        var order: Int
        var isActive: Bool
        var createdAt: Date
        var updatedAt: Date
        
        // Reminder properties
        var reminderEnabled: Bool = false
        var reminderTime: Date?
        var notificationId: String?
        
        // Cache for expensive calculations
        @Transient private var _cachedCurrentStreak: Int?
        @Transient private var _cachedLongestStreak: Int?
        @Transient private var _cachedCompletionRate: Double?
        @Transient private var _cacheLastUpdated: Date?
        
        // Relationships
        @Relationship(deleteRule: .cascade, inverse: \HabitLog.habit)
        var logs: [HabitLog] = []
        
        init(
            name: String,
            icon: String,
            order: Int = 0,
            isActive: Bool = true,
            reminderEnabled: Bool = false,
            reminderTime: Date? = nil
        ) {
            self.id = UUID()
            self.name = name
            self.icon = icon
            self.order = order
            self.isActive = isActive
            self.createdAt = Date()
            self.updatedAt = Date()
            self.reminderEnabled = reminderEnabled
            self.reminderTime = reminderTime
            self.notificationId = nil
        }
    }
    
    @Model
    final class HabitLog {
        var id: UUID
        var date: Date
        var status: HabitStatus
        var completedAt: Date?
        var createdAt: Date
        var updatedAt: Date
        
        // Relationships
        var habit: Habit?
        
        init(habit: Habit, status: HabitStatus = .pending, date: Date = Date()) {
            self.id = UUID()
            self.date = Calendar.current.startOfDay(for: date)
            self.status = status
            self.completedAt = status == .completed ? Date() : nil
            self.createdAt = Date()
            self.updatedAt = Date()
            self.habit = habit
        }
    }
    
    enum HabitStatus: String, Codable, CaseIterable {
        case pending = "pending"
        case completed = "completed"
        case skipped = "skipped"
    }
}

// MARK: - Schema Version 1.1 (Future - Example)
enum HabitSchemaV1_1: VersionedSchema {
    static var versionIdentifier = Schema.Version(1, 1, 0)
    
    static var models: [any PersistentModel.Type] {
        [Habit.self, HabitLog.self]
    }
    
    @Model
    final class Habit {
        var id: UUID
        var name: String
        var icon: String
        var order: Int
        var isActive: Bool
        var createdAt: Date
        var updatedAt: Date
        
        // Reminder properties
        var reminderEnabled: Bool = false
        var reminderTime: Date?
        var notificationId: String?
        
        // NEW: Category support (example future feature)
        var category: String = "general"
        var color: String = "purple"
        
        // Cache for expensive calculations
        @Transient private var _cachedCurrentStreak: Int?
        @Transient private var _cachedLongestStreak: Int?
        @Transient private var _cachedCompletionRate: Double?
        @Transient private var _cacheLastUpdated: Date?
        
        // Relationships
        @Relationship(deleteRule: .cascade, inverse: \HabitLog.habit)
        var logs: [HabitLog] = []
        
        init(
            name: String,
            icon: String,
            order: Int = 0,
            isActive: Bool = true,
            reminderEnabled: Bool = false,
            reminderTime: Date? = nil,
            category: String = "general",
            color: String = "purple"
        ) {
            self.id = UUID()
            self.name = name
            self.icon = icon
            self.order = order
            self.isActive = isActive
            self.createdAt = Date()
            self.updatedAt = Date()
            self.reminderEnabled = reminderEnabled
            self.reminderTime = reminderTime
            self.notificationId = nil
            self.category = category
            self.color = color
        }
    }
    
    @Model
    final class HabitLog {
        var id: UUID
        var date: Date
        var status: HabitStatus
        var completedAt: Date?
        var createdAt: Date
        var updatedAt: Date
        
        // NEW: Optional notes (example future feature)
        var notes: String?
        
        // Relationships
        var habit: Habit?
        
        init(habit: Habit, status: HabitStatus = .pending, date: Date = Date(), notes: String? = nil) {
            self.id = UUID()
            self.date = Calendar.current.startOfDay(for: date)
            self.status = status
            self.completedAt = status == .completed ? Date() : nil
            self.createdAt = Date()
            self.updatedAt = Date()
            self.notes = notes
            self.habit = habit
        }
    }
    
    enum HabitStatus: String, Codable, CaseIterable {
        case pending = "pending"
        case completed = "completed"
        case skipped = "skipped"
    }
}

// MARK: - Migration Plan
enum HabitMigrationPlan: SchemaMigrationPlan {
    static var schemas: [any VersionedSchema.Type] = [
        HabitSchemaV1.self,
        HabitSchemaV1_1.self
    ]
    
    static var stages: [MigrationStage] = [
        // Example migration from v1.0 to v1.1
        MigrationStage.lightweight(fromVersion: HabitSchemaV1.self, toVersion: HabitSchemaV1_1.self)
    ]
}

// MARK: - Migration Utilities
struct MigrationManager {
    static func createModelContainer() -> ModelContainer {
        do {
            let configuration = ModelConfiguration(
                isStoredInMemoryOnly: false,
                cloudKitDatabase: .automatic
            )
            
            return try ModelContainer(
                for: Habit.self, HabitLog.self,
                migrationPlan: HabitMigrationPlan.self,
                configurations: configuration
            )
        } catch {
            print("Migration failed with error: \(error)")
            
            // Fallback to delete and recreate (with user warning in production)
            return createFallbackContainer()
        }
    }
    
    private static func createFallbackContainer() -> ModelContainer {
        // In production, this should show a user dialog warning about data loss
        print("⚠️ WARNING: Creating fresh container - existing data will be lost")
        
        do {
            // Delete existing store files
            let storeURL = URL.applicationSupportDirectory.appending(path: "default.store")
            let fileManager = FileManager.default
            
            if fileManager.fileExists(atPath: storeURL.path) {
                try fileManager.removeItem(at: storeURL)
            }
            
            let configuration = ModelConfiguration(
                isStoredInMemoryOnly: false,
                cloudKitDatabase: .automatic
            )
            
            return try ModelContainer(
                for: Habit.self, HabitLog.self,
                migrationPlan: HabitMigrationPlan.self,
                configurations: configuration
            )
        } catch {
            print("Failed to create fallback container: \(error)")
            fatalError("Could not create ModelContainer: \(error)")
        }
    }
}