//
//  HabitCard.swift
//  SwiftHabits
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct HabitCard: View {
    let habit: Habit
    @Binding var dragOffset: CGSize
    @Binding var isDragging: Bool
    let onComplete: () -> Void
    let onSkip: () -> Void
    
    @State private var showHint = UserDefaults.standard.bool(forKey: "hasCompletedFirstSwipe") ? false : true
    @State private var animateHint = false
    @State private var hapticFeedbackGenerator = UIImpactFeedbackGenerator(style: .light)
    @State private var lastHapticPosition: CGFloat = 0
    
    // Constants
    private let swipeThreshold: CGFloat = 100
    private let rotationMultiplier: Double = 0.15
    private let maxRotation: Double = 15.0
    private let scaleRange: (min: CGFloat, max: CGFloat) = (0.95, 1.0)
    
    // Computed properties
    private var dragPercentage: CGFloat {
        min(abs(dragOffset.width) / swipeThreshold, 1.0)
    }
    
    private var rotationAngle: Double {
        let rotation = Double(dragOffset.width) * rotationMultiplier / 10
        return max(-maxRotation, min(maxRotation, rotation))
    }
    
    private var dynamicScale: CGFloat {
        let normalizedDrag = abs(dragOffset.width) / 200 // Normalize to 0-1 over 200pt
        let scaleReduction = min(normalizedDrag * 0.05, 0.05) // Max 5% reduction
        return isDragging ? (1.0 - scaleReduction) : 1.0
    }
    
    private var swipeDirection: SwipeDirection {
        if dragOffset.width > swipeThreshold {
            return .right
        } else if dragOffset.width < -swipeThreshold {
            return .left
        }
        return .none
    }
    
    private var overlayOpacity: Double {
        switch swipeDirection {
        case .right, .left:
            // Smooth curve for overlay appearance
            let progress = Double(dragPercentage)
            return min(pow(progress, 0.6) * 0.9, 0.9)
        case .none:
            return 0
        }
    }
    
    private var overlayScale: CGFloat {
        switch swipeDirection {
        case .right, .left:
            // Scale up the overlay icon slightly as drag increases
            let progress = dragPercentage
            return 1.0 + (progress * 0.1)
        case .none:
            return 1.0
        }
    }
    
    var body: some View {
        ZStack {
            // Card background with full purple gradient
            cardBackground
            
            // Card content
            VStack(spacing: 16) {
                Spacer()
                
                // Large habit.icon
                Image(systemName: habit.icon)
                    .font(.system(size: 120, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 216) // 45% of 480px height
                
                // Habit name
                Text(habit.name)
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(.horizontal, 30)
                
                // Time or description
                Text(getTimeDescription())
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                // Streak indicator
                if habit.currentStreak > 0 {
                    HStack(spacing: 8) {
                        Image(systemName: "flame.fill")
                            .font(.system(size: 22))
                            .foregroundColor(.white)
                        
                        Text(habit.currentStreak == 1 ? "day_streak".localized(with: habit.currentStreak) : "days_streak".localized(with: habit.currentStreak))
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.white.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 50) {
                    ActionButton(
                        icon: "xmark",
                        color: .habitError,
                        action: handleSkip,
                        isOnPurple: true
                    )
                    
                    ActionButton(
                        icon: "checkmark",
                        color: .habitSuccess,
                        action: handleComplete,
                        isOnPurple: true
                    )
                }
                .padding(.bottom, 40)
            }
            .padding(.horizontal, 20)
            
            // Swipe overlay indicators
            if isDragging {
                swipeOverlay
            }
            
            // First-time user hint
            if showHint {
                swipeHintOverlay
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 480)
        .onAppear {
            // Prepare haptic feedback generator for smooth operation
            hapticFeedbackGenerator.prepare()
        }
        .offset(dragOffset)
        .rotationEffect(.degrees(rotationAngle))
        .scaleEffect(dynamicScale)
        .animation(.easeOut(duration: 0.1), value: isDragging)
        .gesture(
            DragGesture()
                .onChanged { value in
                    // Immediate update without animation for responsive feel
                    isDragging = true
                    
                    // Apply resistance to vertical movement and limit horizontal
                    let horizontalTranslation = value.translation.width
                    let verticalResistance = value.translation.height * 0.3 // 70% resistance
                    
                    dragOffset = CGSize(
                        width: horizontalTranslation,
                        height: verticalResistance
                    )
                    
                    // Continuous haptic feedback based on movement
                    let absWidth = abs(value.translation.width)
                    let hapticInterval: CGFloat = 15 // Haptic every 15 points
                    
                    if absWidth > lastHapticPosition + hapticInterval {
                        // Progressive haptic intensity based on drag distance
                        let intensity: UIImpactFeedbackGenerator.FeedbackStyle
                        if absWidth < swipeThreshold * 0.5 {
                            intensity = .light
                        } else if absWidth < swipeThreshold {
                            intensity = .medium
                        } else {
                            intensity = .heavy
                        }
                        
                        let impact = UIImpactFeedbackGenerator(style: intensity)
                        impact.impactOccurred()
                        lastHapticPosition = absWidth
                    }
                    
                    // Reset haptic position when direction changes or drag reduces
                    if absWidth < lastHapticPosition - hapticInterval {
                        lastHapticPosition = absWidth
                    }
                }
                .onEnded { value in
                    let velocity = value.velocity.width
                    let translation = value.translation.width
                    
                    // Reset haptic tracking
                    lastHapticPosition = 0
                    
                    withAnimation(.easeOut(duration: 0.15)) {
                        isDragging = false
                    }
                    
                    // Consider both translation and velocity for more natural feel
                    let shouldCompleteRight = translation > swipeThreshold || (translation > 50 && velocity > 300)
                    let shouldCompleteLeft = translation < -swipeThreshold || (translation < -50 && velocity < -300)
                    
                    if shouldCompleteRight {
                        // Swipe right - Complete
                        animateCardOffScreen(direction: .right, velocity: velocity) {
                            handleComplete()
                        }
                    } else if shouldCompleteLeft {
                        // Swipe left - Skip
                        animateCardOffScreen(direction: .left, velocity: velocity) {
                            handleSkip()
                        }
                    } else {
                        // Spring back with physics-based animation
                        withAnimation(.interactiveSpring(response: 0.4, dampingFraction: 0.8, blendDuration: 0)) {
                            dragOffset = .zero
                        }
                    }
                }
        )
        // MARK: - Accessibility
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(habit.name) habit")
        .accessibilityHint("swipe_right_complete_left_skip".localized)
        .accessibilityAddTraits(.allowsDirectInteraction)
        .accessibilityAction(named: "complete_habit".localized) {
            handleComplete()
        }
        .accessibilityAction(named: "skip_habit".localized) {
            handleSkip()
        }

    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 30)
            .fill(LinearGradient.habitPurpleGradient)
            .shadow(color: Color.habitPurple.opacity(0.3), radius: 20, x: 0, y: 10)
    }
    
    // MARK: - Swipe Overlay
    private var swipeOverlay: some View {
        ZStack {
            if swipeDirection == .right {
                // Complete overlay - Green for DONE
                RoundedRectangle(cornerRadius: 30)
                    .fill(Color.habitSuccess.opacity(overlayOpacity * 0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 30)
                            .stroke(Color.habitSuccess.opacity(overlayOpacity * 0.8), lineWidth: 3)
                    )
                    .overlay(
                        VStack(spacing: 12) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.habitSuccess)
                                .scaleEffect(overlayScale)
                            Text("done".localized.uppercased())
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.habitSuccess)
                        }
                        .opacity(overlayOpacity)
                        .animation(.easeOut(duration: 0.1), value: overlayOpacity)
                    )
            } else if swipeDirection == .left {
                // Skip overlay - Red for SKIP
                RoundedRectangle(cornerRadius: 30)
                    .fill(Color.habitError.opacity(overlayOpacity * 0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 30)
                            .stroke(Color.habitError.opacity(overlayOpacity * 0.8), lineWidth: 3)
                    )
                    .overlay(
                        VStack(spacing: 12) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.habitError)
                                .scaleEffect(overlayScale)
                            Text("skip".localized.uppercased())
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.habitError)
                        }
                        .opacity(overlayOpacity)
                        .animation(.easeOut(duration: 0.1), value: overlayOpacity)
                    )
            }
        }
    }
    
    // MARK: - Swipe Hint Overlay
    private var swipeHintOverlay: some View {
        HStack {
            // Left hint
            VStack(spacing: 8) {
                Image(systemName: "arrow.left.circle.fill")
                    .font(.system(size: 36))
                    .foregroundColor(.habitError)
                Text("skip".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.habitTextSecondary)
            }
            .offset(x: animateHint ? -10 : 0)
            
            Spacer()
            
            // Right hint
            VStack(spacing: 8) {
                Image(systemName: "arrow.right.circle.fill")
                    .font(.system(size: 36))
                    .foregroundColor(.habitSuccess)
                Text("done".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.habitTextSecondary)
            }
            .offset(x: animateHint ? 10 : 0)
        }
        .padding(.horizontal, 40)
        .opacity(0.8)
        .onAppear {
            withAnimation(.longBackground.repeatForever(autoreverses: true)) {
                animateHint = true
            }
        }
    }
    
    // MARK: - Actions
    private func handleComplete() {
        let impact = UIImpactFeedbackGenerator(style: .heavy)
        impact.impactOccurred()
        
        // Mark that user has completed first swipe
        if showHint {
            UserDefaults.standard.set(true, forKey: "hasCompletedFirstSwipe")
            showHint = false
        }
        
        onComplete()
        resetCard()
    }
    
    private func handleSkip() {
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
        
        // Mark that user has completed first swipe
        if showHint {
            UserDefaults.standard.set(true, forKey: "hasCompletedFirstSwipe")
            showHint = false
        }
        
        onSkip()
        resetCard()
    }
    
    private func animateCardOffScreen(direction: SwipeDirection, velocity: CGFloat = 0, completion: @escaping () -> Void) {
        // Calculate the distance based on screen size and add some buffer
        let screenWidth = UIScreen.main.bounds.width
        let exitDistance = screenWidth + 100
        
        // Adjust animation duration based on velocity for natural feel
        let baseResponse: Double = 0.4
        let velocityFactor = min(abs(velocity) / 1000, 0.2) // Cap velocity influence
        let dynamicResponse = max(baseResponse - velocityFactor, 0.2)
        
        withAnimation(.interactiveSpring(response: dynamicResponse, dampingFraction: 0.8, blendDuration: 0)) {
            switch direction {
            case .right:
                dragOffset = CGSize(width: exitDistance, height: dragOffset.height * 0.5)
            case .left:
                dragOffset = CGSize(width: -exitDistance, height: dragOffset.height * 0.5)
            case .none:
                break
            }
        }
        
        // Timing adjusted based on dynamic response
        DispatchQueue.main.asyncAfter(deadline: .now() + dynamicResponse + 0.1) {
            completion()
        }
    }
    
    private func resetCard() {
        dragOffset = .zero
    }
    
    private func getStreakMessage(_ streak: Int) -> String {
        switch streak {
        case 7..<14:
            return "1 week strong! 💪"
        case 14..<30:
            return "2 weeks! Amazing! 🔥"
        case 30..<60:
            return "1 month! Incredible! 🌟"
        case 60..<90:
            return "2 months! Unstoppable! 🚀"
        case 90...:
            return "3+ months! Legend! 👑"
        default:
            return "Keep it up!"
        }
    }
    
    private func getTimeDescription() -> String {
        if habit.reminderEnabled, let reminderTime = habit.reminderTime {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            
            let calendar = Calendar.current
            let now = Date()
            let reminderHour = calendar.component(.hour, from: reminderTime)
            let reminderMinute = calendar.component(.minute, from: reminderTime)
            let currentHour = calendar.component(.hour, from: now)
            let currentMinute = calendar.component(.minute, from: now)
            
            // Check if reminder is within the next hour
            let reminderMinutes = reminderHour * 60 + reminderMinute
            let currentMinutes = currentHour * 60 + currentMinute
            let diff = (reminderMinutes - currentMinutes + 1440) % 1440
            
            if diff > 0 && diff <= 60 {
                return "\(formatter.string(from: reminderTime)) • Due soon"
            } else {
                return formatter.string(from: reminderTime)
            }
        } else {
            // Fallback to generic time description
            let hour = Calendar.current.component(.hour, from: Date())
            switch hour {
            case 5..<9:
                return "morning_habit".localized
            case 9..<12:
                return "mid_morning".localized
            case 12..<14:
                return "lunch_time".localized
            case 14..<17:
                return "afternoon".localized
            case 17..<20:
                return "evening".localized
            case 20..<23:
                return "night_routine".localized
            default:
                return "daily_habit".localized
            }
        }
    }
}

// MARK: - Supporting Types
enum SwipeDirection {
    case left, right, none
}

struct ActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void
    var isOnPurple: Bool = false
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.quickInteraction) {
                action()
            }
        }) {
            ZStack {
                // White background with shadow
                Circle()
                    .fill(Color.white)
                    .frame(width: 68, height: 68)
                    .shadow(color: isOnPurple ? Color.black.opacity(0.15) : .habitShadow, radius: 8, x: 0, y: 4)
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 30, weight: .semibold))
                    .foregroundColor(color)
            }
            .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(icon == "checkmark" ? "complete_habit".localized : "skip_habit".localized)
        .accessibilityHint(icon == "checkmark" ? "mark_habit_complete".localized : "skip_habit_today".localized)
        .accessibilityAddTraits(.isButton)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.quickInteraction) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

#Preview {
    ZStack {
        Color.habitBackground
            .ignoresSafeArea()
        
        let sampleHabit = Habit(
            name: "Morning Meditation",
            icon: "brain.head.profile",
            reminderEnabled: true,
            reminderTime: Date()
        )
        
        HabitCard(
            habit: sampleHabit,
            dragOffset: .constant(.zero),
            isDragging: .constant(false),
            onComplete: { },
            onSkip: { }
        )
        .padding()
    }
}