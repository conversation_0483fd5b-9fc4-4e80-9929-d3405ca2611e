//
//  ExportDataView.swift
//  SwiftHabits
//
//  Data export functionality with GDPR compliance
//

import SwiftUI
import SwiftData

struct ExportDataView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var habits: [Habit]
    
    @State private var selectedFormat: ExportFormat = .json
    @State private var includeDeletedHabits = false
    @State private var dateRange: DateRange = .all
    @State private var isExporting = false
    @State private var exportedData: Data?
    @State private var showShareSheet = false
    @State private var showDeleteConfirmation = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    enum ExportFormat: String, CaseIterable {
        case json = "JSON"
        case csv = "CSV"
        
        var fileExtension: String {
            switch self {
            case .json: return "json"
            case .csv: return "csv"
            }
        }
    }
    
    enum DateRange: String, CaseIterable {
        case all = "all_time"
        case lastMonth = "last_month"
        case lastThreeMonths = "last_3_months"
        case lastYear = "last_year"
        
        var dateFilter: Date? {
            let calendar = Calendar.current
            let now = Date()
            
            switch self {
            case .all:
                return nil
            case .lastMonth:
                return calendar.date(byAdding: .month, value: -1, to: now)
            case .lastThreeMonths:
                return calendar.date(byAdding: .month, value: -3, to: now)
            case .lastYear:
                return calendar.date(byAdding: .year, value: -1, to: now)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    headerSection
                    formatSelectionSection
                    optionsSection
                    exportButtonSection
                    dataManagementSection
                }
                .padding()
            }
            .background(Color.habitBackground)
            .navigationTitle("export_data".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized) {
                        dismiss()
                    }
                    .foregroundColor(.habitPurple)
                }
            }
        }
        .sheet(isPresented: $showShareSheet) {
            if let data = exportedData {
                ShareSheet(
                    activityItems: [createExportFile(data: data)],
                    applicationActivities: nil
                )
            }
        }
        .alert("delete_all_data".localized, isPresented: $showDeleteConfirmation) {
            Button("cancel".localized, role: .cancel) { }
            Button("delete".localized, role: .destructive, action: deleteAllData)
        } message: {
            Text("delete_permanently_warning".localized)
        }
        .alert("error".localized, isPresented: $showErrorAlert) {
            Button("ok".localized) { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("export_your_data".localized)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.habitTextPrimary)

            Text("export_description".localized)
                .font(.body)
                .foregroundColor(.habitTextSecondary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
    
    private var formatSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("export_format".localized)
                .font(.headline)
                .foregroundColor(.habitTextPrimary)
            
            HStack(spacing: 12) {
                ForEach(ExportFormat.allCases, id: \.self) { format in
                    Button(action: {
                        selectedFormat = format
                    }) {
                        HStack {
                            Image(systemName: selectedFormat == format ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(selectedFormat == format ? .habitPurple : .habitTextSecondary)
                            Text(format.rawValue)
                                .foregroundColor(.habitTextPrimary)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedFormat == format ? Color.habitPurple.opacity(0.1) : Color.habitCardBackground)
                        )
                    }
                }
            }
        }
    }
    
    private var optionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("export_options".localized)
                .font(.headline)
                .foregroundColor(.habitTextPrimary)

            VStack(spacing: 12) {
                HStack {
                    Text("date_range".localized)
                        .foregroundColor(.habitTextPrimary)
                    Spacer()
                    Picker("date_range".localized, selection: $dateRange) {
                        ForEach(DateRange.allCases, id: \.self) { range in
                            Text(range.rawValue.localized).tag(range)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .tint(.habitPurple)
                }
                
                Toggle(isOn: $includeDeletedHabits) {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("include_inactive_habits".localized)
                            .foregroundColor(.habitTextPrimary)
                        Text("export_archived_habits".localized)
                            .font(.caption)
                            .foregroundColor(.habitTextSecondary)
                    }
                }
                .tint(.habitPurple)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.habitCardBackground)
            )
        }
    }
    
    private var exportButtonSection: some View {
        VStack(spacing: 12) {
            Button(action: exportData) {
                HStack {
                    if isExporting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "square.and.arrow.up")
                    }
                    Text(isExporting ? "preparing_export".localized : "export_data".localized)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.habitPurple)
                )
            }
            .disabled(isExporting)
            
            Text("exported_data_contains".localized)
                .font(.caption)
                .foregroundColor(.habitTextSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var dataManagementSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("data_management".localized)
                .font(.headline)
                .foregroundColor(.habitTextPrimary)

            VStack(spacing: 12) {
                Button(action: {
                    showDeleteConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "trash")
                        Text("delete_all_data".localized)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                    }
                    .foregroundColor(.red)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.red.opacity(0.1))
                    )
                }
                
                Text("delete_all_warning".localized)
                    .font(.caption)
                    .foregroundColor(.habitTextSecondary)
            }
        }
    }
    
    private func exportData() {
        isExporting = true
        
        Task {
            do {
                let data = try await generateExportData()
                await MainActor.run {
                    exportedData = data
                    isExporting = false
                    showShareSheet = true
                }
            } catch {
                await MainActor.run {
                    isExporting = false
                    errorMessage = "export_failed_message".localized
                    showErrorAlert = true
                    print("Export failed: \(error)")
                }
            }
        }
    }
    
    private func generateExportData() async throws -> Data {
        let filteredHabits = habits.filter { habit in
            includeDeletedHabits || habit.isActive
        }.map { habit in
            let filteredLogs = habit.logs.filter { log in
                guard let dateFilter = dateRange.dateFilter else { return true }
                return log.date >= dateFilter
            }
            
            return ExportHabit(
                id: habit.id.uuidString,
                name: habit.name,
                icon: habit.icon,
                isActive: habit.isActive,
                createdAt: habit.createdAt,
                updatedAt: habit.updatedAt,
                reminderEnabled: habit.reminderEnabled,
                reminderTime: habit.reminderTime,
                currentStreak: habit.currentStreak,
                longestStreak: habit.longestStreak,
                completionRate: habit.completionRate,
                logs: filteredLogs.map { log in
                    ExportHabitLog(
                        id: log.id.uuidString,
                        date: log.date,
                        status: log.status.rawValue,
                        completedAt: log.completedAt
                    )
                }
            )
        }
        
        let exportData = ExportData(
            exportedAt: Date(),
            version: "1.0",
            format: selectedFormat.rawValue,
            habits: filteredHabits
        )
        
        switch selectedFormat {
        case .json:
            return try JSONEncoder().encode(exportData)
        case .csv:
            return try generateCSVData(from: filteredHabits)
        }
    }
    
    private func generateCSVData(from habits: [ExportHabit]) throws -> Data {
        var csvContent = "\(String(localized: "csv_header_habit_name")),\(String(localized: "csv_header_icon")),\(String(localized: "csv_header_status")),\(String(localized: "csv_header_created_date")),\(String(localized: "csv_header_current_streak")),\(String(localized: "csv_header_longest_streak")),\(String(localized: "csv_header_completion_rate")),\(String(localized: "csv_header_log_date")),\(String(localized: "csv_header_log_status")),\(String(localized: "csv_header_completed_at"))\n"
        
        for habit in habits {
            let statusText = habit.isActive ? String(localized: "status_active") : String(localized: "status_inactive")
            let baseRow = "\"\(habit.name)\",\(habit.icon),\(statusText),\(ISO8601DateFormatter().string(from: habit.createdAt)),\(habit.currentStreak),\(habit.longestStreak),\(String(format: "%.2f", habit.completionRate))"
            
            if habit.logs.isEmpty {
                csvContent += "\(baseRow),,,\n"
            } else {
                for log in habit.logs {
                    let completedAtString = log.completedAt.map { ISO8601DateFormatter().string(from: $0) } ?? ""
                    let logStatusText = log.status == "completed" ? String(localized: "status_completed") : String(localized: "status_skipped")
                    csvContent += "\(baseRow),\(ISO8601DateFormatter().string(from: log.date)),\(logStatusText),\(completedAtString)\n"
                }
            }
        }
        
        return csvContent.data(using: .utf8) ?? Data()
    }
    
    private func createExportFile(data: Data) -> URL {
        let fileName = "SwiftHabits-Export-\(DateFormatter.fileNameFormatter.string(from: Date())).\(selectedFormat.fileExtension)"
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        try? data.write(to: tempURL)
        return tempURL
    }
    
    private func deleteAllData() {
        // Delete all habits (cascades to logs)
        for habit in habits {
            modelContext.delete(habit)
        }
        
        try? modelContext.save()
        dismiss()
    }
}

// MARK: - Export Data Models
private struct ExportData: Codable {
    let exportedAt: Date
    let version: String
    let format: String
    let habits: [ExportHabit]
}

private struct ExportHabit: Codable {
    let id: String
    let name: String
    let icon: String
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    let reminderEnabled: Bool
    let reminderTime: Date?
    let currentStreak: Int
    let longestStreak: Int
    let completionRate: Double
    let logs: [ExportHabitLog]
}

private struct ExportHabitLog: Codable {
    let id: String
    let date: Date
    let status: String
    let completedAt: Date?
}

// MARK: - Share Sheet
private struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]?
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Extensions
private extension DateFormatter {
    static let fileNameFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter
    }()
}