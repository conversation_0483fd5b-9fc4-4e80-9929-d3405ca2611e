//
//  BottomNavigationBar.swift
//  SwiftHabits
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct BottomNavigationBar: View {
    @Binding var selectedTab: String
    let onAddTap: () -> Void
    
    var body: some View {
        HStack(spacing: 0) {
            // Home
            TabBarButton(
                icon: "house",
                selectedIcon: "house.fill",
                title: "home".localized,
                isSelected: selectedTab == "home",
                action: { selectedTab = "home" }
            )
            .frame(width: 120) // Fixed width for compact design
            
            // Add button (middle)
            But<PERSON>(action: onAddTap) {
                ZStack {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 56, height: 56)
                        .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                    
                    Image(systemName: "plus")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.habitPurple)
                }
            }
            .accessibilityLabel("add_new_habit".localized)
            .accessibilityHint("tap_to_add_habit".localized)
            .accessibilityAddTraits(.isButton)
            .frame(width: 80) // Compact middle section
            
            // Activity
            TabBarButton(
                icon: "chart.bar",
                selectedIcon: "chart.bar.fill",
                title: "activity".localized,
                isSelected: selectedTab == "activity",
                action: { selectedTab = "activity" }
            )
            .frame(width: 120) // Fixed width for compact design
        }
        .frame(width: 320) // Total compact width
        .padding(.vertical, 12)
        .background(
            LinearGradient.habitBottomNavGradient
                .clipShape(RoundedRectangle(cornerRadius: 30))
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
}

struct TabBarButton: View {
    let icon: String
    let selectedIcon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: isSelected ? selectedIcon : icon)
                    .font(.system(size: 22))
                    .foregroundColor(.white)
                Text(title)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(.white.opacity(isSelected ? 1 : 0.7))
            }
        }
        .accessibilityLabel(title)
        .accessibilityHint("tab_to_navigate".localized(with: title))
        .accessibilityAddTraits(.isButton)
        .accessibilityAddTraits(isSelected ? .isSelected : [])
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    ZStack {
        Color.habitBackground
            .ignoresSafeArea()
        
        VStack {
            Spacer()
            BottomNavigationBar(
                selectedTab: .constant("home"),
                onAddTap: { }
            )
        }
    }
}