//
//  EmptyStateCard.swift
//  SwiftHabits
//
//  Empty state card with onboarding hints
//

import SwiftUI

struct EmptyStateCard: View {
    @State private var showSwipeHint = true
    @State private var dragOffset: CGSize = .zero
    @State private var animatingHint = false
    @State private var sparkleAnimation = false
    @State private var selectedQuickHabit: Int? = nil
    @State private var showingAddHabit = false
    
    var onAddHabit: () -> Void
    var onBrowseTemplates: () -> Void
    
    private let quickHabits = [
        (icon: "drop.fill", name: "drink_water", color: Color.blue),
        (icon: "figure.walk", name: "daily_walk", color: Color.habitSuccess),
        (icon: "book.fill", name: "read_daily", color: Color.habitOrange),
        (icon: "moon.fill", name: "sleep_early", color: Color.habitPurple)
    ]
    
    var body: some View {
        ZStack {
            // Main card
            VStack(spacing: 32) {
                // Animated illustration
                ZStack {
                    // Background circles
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(LinearGradient.habitPurpleGradient.opacity(0.1))
                            .frame(width: 140 - CGFloat(index * 20), height: 140 - CGFloat(index * 20))
                            .scaleEffect(sparkleAnimation ? 1.1 : 0.9)
                            .animation(
                                .easeInOut(duration: 2.0)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                                value: sparkleAnimation
                            )
                    }
                    
                    // Sparkle effects
                    ForEach(0..<4) { index in
                        Image(systemName: "sparkle")
                            .font(.system(size: 16))
                            .foregroundColor(.habitPurple)
                            .offset(
                                x: cos(Double(index) * .pi / 2) * 60,
                                y: sin(Double(index) * .pi / 2) * 60
                            )
                            .scaleEffect(sparkleAnimation ? 1.0 : 0.5)
                            .opacity(sparkleAnimation ? 0.8 : 0.3)
                            .animation(
                                .easeInOut(duration: 1.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.3),
                                value: sparkleAnimation
                            )
                    }
                    
                    // Center icon
                    Image(systemName: "star.fill")
                        .font(.system(size: 50))
                        .foregroundStyle(LinearGradient.habitPurpleGradient)
                        .rotationEffect(.degrees(sparkleAnimation ? 10 : -10))
                        .animation(
                            .easeInOut(duration: 3.0)
                            .repeatForever(autoreverses: true),
                            value: sparkleAnimation
                        )
                }
                .frame(height: 140)
                
                VStack(spacing: 16) {
                    Text(String(localized: "ready_to_start"))
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                    
                    Text(String(localized: "build_better_habits"))
                        .font(.system(size: 18))
                        .foregroundColor(.habitTextSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                }
                
                // Action buttons
                VStack(spacing: 12) {
                    // Create new habit button
                    Button(action: onAddHabit) {
                        HStack(spacing: 12) {
                            Image(systemName: "plus")
                                .font(.system(size: 18, weight: .semibold))
                            
                            Text(String(localized: "create_new_habit"))
                                .font(.system(size: 17, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 18)
                        .background(LinearGradient.habitPurpleGradient)
                        .cornerRadius(16)
                        .shadow(color: .habitPurple.opacity(0.3), radius: 10, x: 0, y: 5)
                    }
                    
                    // Browse templates button
                    Button(action: onBrowseTemplates) {
                        HStack(spacing: 12) {
                            Image(systemName: "square.grid.2x2")
                                .font(.system(size: 16, weight: .medium))
                            
                            Text(String(localized: "browse_habit_templates"))
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundColor(.habitPurple)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(Color.habitPurple.opacity(0.1))
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.habitPurple.opacity(0.3), lineWidth: 1)
                        )
                    }
                }
                .padding(.horizontal, 20)
                
            }
            .padding(.vertical, 40)
            .padding(.horizontal, 20)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 30)
                    .fill(.ultraThinMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 30)
                            .fill(Color.white)
                    )
                    .shadow(color: .habitShadow, radius: 20, x: 0, y: 10)
            )
            .offset(dragOffset)
            .rotationEffect(.degrees(Double(dragOffset.width / 20)))
            .gesture(
                DragGesture()
                    .onChanged { value in
                        withAnimation(.interactiveSpring()) {
                            dragOffset = value.translation
                        }
                    }
                    .onEnded { value in
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.6)) {
                            dragOffset = .zero
                        }
                        
                        // Hide hint after first interaction
                        if abs(value.translation.width) > 50 {
                            withAnimation {
                                showSwipeHint = false
                            }
                        }
                    }
            )
            
        }
        .onAppear {
            animatingHint = true
        }
    }
}

// MARK: - Quick Habit Button
struct QuickHabitButton: View {
    let icon: String
    let name: String
    let color: Color
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(isSelected ? .white : color)
                    .frame(width: 50, height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isSelected ? color : color.opacity(0.1))
                    )
                
                Text(String(localized: String.LocalizationValue(name)))
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(isSelected ? color : .habitTextSecondary)
                    .lineLimit(1)
            }
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

#Preview {
    EmptyStateCard(onAddHabit: {}, onBrowseTemplates: {})
        .padding()
        .background(Color.habitBackground)
}