//
//  PrivacyPolicyView.swift
//  SwiftHabits
//
//  Privacy policy display with GDPR compliance
//

import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text(String(localized: "privacy_policy"))
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.habitTextPrimary)
                        .padding(.bottom, 10)
                    
                    Group {
                        privacySection(
                            title: String(localized: "data_we_collect"),
                            content: String(localized: "privacy_data_collect_text")
                        )
                        
                        privacySection(
                            title: String(localized: "how_we_use_your_data"),
                            content: String(localized: "privacy_data_use_text")
                        )
                        
                        privacySection(
                            title: String(localized: "data_storage_security"),
                            content: String(localized: "privacy_data_storage_text")
                        )
                        
                        privacySection(
                            title: String(localized: "your_rights_gdpr"),
                            content: String(localized: "privacy_rights_text")
                        )
                        
                        privacySection(
                            title: String(localized: "notifications"),
                            content: String(localized: "privacy_notifications_text")
                        )
                        
                        privacySection(
                            title: String(localized: "third_party_services"),
                            content: String(localized: "privacy_third_party_text")
                        )
                        
                        privacySection(
                            title: String(localized: "contact_us"),
                            content: String(localized: "privacy_contact_text", defaultValue: "Questions about this privacy policy or your data?\nContact us at: <EMAIL>\n\nLast updated: %@", table: nil).replacingOccurrences(of: "%@", with: formattedDate())
                        )
                    }
                }
                .padding()
            }
            .background(Color.habitBackground)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(String(localized: "done")) {
                        dismiss()
                    }
                    .foregroundColor(.habitPurple)
                }
            }
        }
    }
    
    @ViewBuilder
    private func privacySection(title: String, content: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.habitTextPrimary)
            
            Text(content)
                .font(.body)
                .foregroundColor(.habitTextSecondary)
                .lineSpacing(4)
        }
        .padding(.vertical, 8)
    }
    
    private func formattedDate() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        return formatter.string(from: Date())
    }
}

struct PrivacyPolicyView_Previews: PreviewProvider {
    static var previews: some View {
        PrivacyPolicyView()
    }
}