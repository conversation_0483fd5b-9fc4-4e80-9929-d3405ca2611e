//
//  String+Localization.swift
//  SwiftHabits
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 25..
//

import Foundation

extension String {
    /// Returns a localized version of the string
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// Returns a localized string with arguments
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

// MARK: - Convenience properties for common strings
extension String {
    static let save = "save".localized
    static let cancel = "cancel".localized
    static let delete = "delete".localized
    static let edit = "edit".localized
    static let done = "done".localized
    static let ok = "ok".localized
    
    static let home = "home".localized
    static let activity = "activity".localized
    static let settings = "settings".localized
    
    static let newHabit = "new_habit".localized
    static let editHabit = "edit_habit".localized
    static let manageHabits = "manage_habits".localized
}