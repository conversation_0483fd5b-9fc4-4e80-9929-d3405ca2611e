//
//  AnimationConstants.swift
//  app
//
//  Created by Lightning Habit on 2025-01-24.
//

import SwiftUI

/// Centralized animation constants following Apple's design guidelines
/// and ensuring consistent user experience across the app
struct AnimationConstants {
    
    // MARK: - Spring Animation Values
    
    /// Standard damping fraction for most animations
    /// Apple recommends 0.8 for natural, responsive feel
    static let standardDamping: Double = 0.8
    
    /// Softer damping for celebration and milestone animations
    static let softDamping: Double = 0.7
    
    /// Tighter damping for quick interactions
    static let tightDamping: Double = 0.9
    
    // MARK: - Response Times
    
    /// Quick interactions (button presses, icon changes)
    /// Apple guideline: 0.2-0.3s for immediate feedback
    static let quickResponse: Double = 0.3
    
    /// Standard interactions (card swipes, view transitions)
    /// Apple guideline: 0.4-0.5s for smooth transitions
    static let standardResponse: Double = 0.4
    
    /// Slow presentations (view appearances, celebrations)
    /// Apple guideline: 0.6-0.8s for dramatic effect
    static let slowResponse: Double = 0.6
    
    /// Celebration animations (milestones, completions)
    static let celebrationResponse: Double = 0.5
    
    // MARK: - Pre-defined Spring Animations
    
    /// Quick spring for immediate user feedback
    static let quickSpring = Animation.spring(
        response: quickResponse,
        dampingFraction: standardDamping
    )
    
    /// Standard spring for most UI interactions
    static let standardSpring = Animation.spring(
        response: standardResponse,
        dampingFraction: standardDamping
    )
    
    /// Slow spring for view presentations
    static let slowSpring = Animation.spring(
        response: slowResponse,
        dampingFraction: standardDamping
    )
    
    /// Celebration spring for achievements and milestones
    static let celebrationSpring = Animation.spring(
        response: celebrationResponse,
        dampingFraction: softDamping
    )
    
    /// Tight spring for precise interactions
    static let tightSpring = Animation.spring(
        response: quickResponse,
        dampingFraction: tightDamping
    )
    
    // MARK: - Easing Animations
    
    /// Standard easing for fade effects
    static let standardEase = Animation.easeInOut(duration: 0.3)
    
    /// Long easing for background animations
    static let longEase = Animation.easeInOut(duration: 1.0)
    
    /// Background cycle animation (for subtle effects)
    static let backgroundCycle = Animation.easeInOut(duration: 8.0)
    
    /// Quick fade out
    static let quickFadeOut = Animation.easeOut(duration: 0.5)
    
    // MARK: - Delay Values
    
    /// Base delay for staggered animations
    static let baseDelay: Double = 0.1
    
    /// Standard delay increment for sequential animations
    static let delayIncrement: Double = 0.1
    
    /// Message delay for completion views
    static let messageDelay: Double = 0.3
    
    // MARK: - Helper Functions
    
    /// Creates a staggered delay based on index
    /// - Parameter index: The index of the item in sequence
    /// - Returns: Calculated delay value
    static func staggeredDelay(for index: Int) -> Double {
        return baseDelay + (Double(index) * delayIncrement)
    }
    
    /// Creates a dynamic delay for collections
    /// - Parameters:
    ///   - index: The index of the item
    ///   - baseDelay: The base delay to start from
    ///   - increment: The increment between items
    /// - Returns: Calculated delay value
    static func dynamicDelay(index: Int, baseDelay: Double = 0.4, increment: Double = 0.1) -> Double {
        return baseDelay + (Double(index) * increment)
    }
}

// MARK: - Animation Extensions

extension Animation {
    
    /// Quick interaction animation
    static var quickInteraction: Animation {
        AnimationConstants.quickSpring
    }
    
    /// Standard UI animation
    static var standardUI: Animation {
        AnimationConstants.standardSpring
    }
    
    /// Slow presentation animation
    static var slowPresentation: Animation {
        AnimationConstants.slowSpring
    }
    
    /// Celebration animation
    static var celebration: Animation {
        AnimationConstants.celebrationSpring
    }
    
    /// Tight precision animation
    static var tightPrecision: Animation {
        AnimationConstants.tightSpring
    }
    
    /// Standard fade animation
    static var standardFade: Animation {
        AnimationConstants.standardEase
    }
    
    /// Long background animation
    static var longBackground: Animation {
        AnimationConstants.longEase
    }
}
