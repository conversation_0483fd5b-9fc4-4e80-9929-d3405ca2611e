//
//  Colors.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

extension Color {
    // Primary colors - Modern purple theme
    static let habitPurple = Color(red: 0.40, green: 0.31, blue: 0.78)
    static let habitPurpleDark = Color(red: 0.31, green: 0.22, blue: 0.65)
    static let habitPurpleLight = Color(red: 0.52, green: 0.45, blue: 0.85)
    static let habitOrange = Color(red: 1.0, green: 0.58, blue: 0.31)
    
    // Background colors
    static let habitBackground = Color(red: 0.98, green: 0.97, blue: 1.0) // Very light purple tint
    static let habitCardBackground = Color.white
    static let habitSurfaceLight = Color(red: 0.96, green: 0.95, blue: 0.99)
    
    // Text colors
    static let habitTextPrimary = Color(red: 0.12, green: 0.12, blue: 0.14)
    static let habitTextSecondary = Color(red: 0.52, green: 0.52, blue: 0.56)
    static let habitTextTertiary = Color(red: 0.68, green: 0.68, blue: 0.72)
    
    // UI Element colors
    static let habitDivider = Color(red: 0.90, green: 0.90, blue: 0.93)
    static let habitShadow = Color.black.opacity(0.06)
    static let habitSuccess = Color(red: 0.34, green: 0.75, blue: 0.44)
    static let habitWarning = Color(red: 1.0, green: 0.58, blue: 0.31)
    static let habitError = Color(red: 0.91, green: 0.34, blue: 0.34)
}

// Gradient definitions
extension LinearGradient {
    static let habitPurpleGradient = LinearGradient(
        colors: [Color.habitPurple, Color.habitPurpleDark],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let habitBottomNavGradient = LinearGradient(
        colors: [Color.habitPurple, Color.habitPurpleDark],
        startPoint: .leading,
        endPoint: .trailing
    )
    
    static let habitCardGradient = LinearGradient(
        colors: [Color.habitPurpleLight.opacity(0.1), Color.habitPurple.opacity(0.05)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}