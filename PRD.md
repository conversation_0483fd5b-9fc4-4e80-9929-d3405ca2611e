# 📋 Product Requirements Document  
**Project:** LightningHabit  
**Target Audience:** iOS users seeking a minimalist, ultra-fast, joyfully simple habit-tracking experience

---

## 1. Problem Statement

Current habit apps suffer from recurring user pain points:

- **Streaks**  
  - Unreliable notifications & reminders, often firing at wrong times or not at all  
  - UI inconsistency and difficulty navigating simple tasks  

- **Habitify**  
  - Friction from subscription paywalls; users dislike being pushed to upgrade  

- **Haby**  
  - Reported force-closes, tap delays, and occasional data loss  

- **Loop / other minimal apps**  
  - Lack cloud backup, iOS versions, streak-skip options  

---

## 2. Vision & Objectives

**LightningHabit** will deliver a habit-tracker that is:

- **Blazing fast** — open app and log habit within 2 seconds  
- **Single-screen simplicity** — no menus, no tabs, just checklist & stats  
- **Consistent & polished UI/UX** — intuitive animations, no layout quirks  
- **Reliable notifications** — smart reminders that never interrupt ongoing activation  
- **Offline-first & cloud-safe** — full local use with optional iCloud backup  
- **Minimalist, yet professional** — clean aesthetics and meaningful motion  

---

## 3. Key Features

### 3.1 Core Experience
- **Instant checklist** on launch — habits listed with large tappable cells  
- **Smart swipes/taps** to mark done → animated check, seamless haptic feedback  
- **Daily summary** after last habit, showing streaks & completion percentage

### 3.2 Notifications
- **Dynamic reminders** adapt to user behavior (no reminders while active)  
- **Streak freeze option** for skipped days (inspired by Loop)  

### 3.3 Data & Sync
- **Local-first storage** with automatic iCloud backup  
- **Exportable logs** in JSON/CSV format  
- **No forced signup**

### 3.4 UX Design — LightningHabit "Swipe" + Simplicity Edition ⚡️

#### ✨ Ultra‑Simple, Lightning‑Fast Interaction
- **One‑screen simplicity**: habit card jelenik meg azonnal app megnyitáskor – nincs menü, nincs lapozás, csak azonnal kezdheted a napodat
- **Fastest path to action**: habit log as quickly as 1–2 swipes or taps – no friction, no extra taps
- **Minimal cognitive load**: removing clutter and focusing only on core action increases user retention and satisfaction
- **Instant feedback**: subtle haptic + visual confirmation after each swipe/tap—makes interaction feel satisfying and rewarding

#### 🔁 Tinder‑Style Swiping — Intuitive & Engaging
- **Swipe right → Done**, **Swipe left → Skip/fail** (user-configurable) using clear color cues (green/red), bounce rotation, and 60fps animations
- **Tap checkbox** is also available as fallback (accessibility-first)
- **Undo button** appears briefly after swipes—prevents accidental actions
- **Playful habit loops** via swipe mechanics trigger curiosity & reward, similar to Tinder's engagement design

#### 🚀 Speed & Performance
- Designed for **instant launch → log habit under 2 seconds**
- Built in **SwiftUI + Core Animation**, optimized for smooth 60fps even on older iPhones
- **Minimal codebase**, fast startup time, no delays

#### 🎯 Visual Clarity & Accessibility
- **High contrast**, clean typography, single card focus—zero distractions
- Gesture zones within thumb reach; tap/fallback ensures accessibility for all
- Onboarding overlays show simple "Swipe right = done, left = skip" hints only once; then fade to never disturb again

#### ✅ Why This Approach Wins
- **Simplicity** means users instantly know what to do—no learning curve, no menus
- **Speed**: actions done in under 2 swipes/taps—perfect for daily habit logging
- **Intuitive**: swipe gestures are already familiar; add checkbox fallback for clarity
- **Delightful**: smooth animation + haptics turns habit tracking into a satisfying, repeatable experience

---

## 4. Technical Stack & Tools

- **Language & Framework:** Swift + SwiftUI  
- **IDE & Docs:** Full leverage of `apple-doc-mcp` and Xcode MCP automation for code-gen, documentation, testing  
- **Architecture:** Clean MVVM  
- **Persistence:** SwiftData with iCloud sync  
- **Analytics (optional):** Only anonymous, local usage metrics

---

## 5. Differentiators vs Competitors

| Pain Point              | LightningHabit Approach |
|------------------------|-------------------------|
| Buggy notifications (Streaks) | Smart reminders, interruption avoidance |
| UI inconsistency       | Single-screen, no navigation puzzles |
| Tap delays / crashes (Haby) | Rigorous QA, lightweight code, instrumentation |
| Subscription pressure (Habitify) | One-time purchase, optional upgrade, no paywall friction |
| Lack of streak flexibility (Loop) | Built-in skip-days feature, adjustable streak logic |
| Cloud & backup missing | Local-first + iCloud backup + export options |

---

## 6. MVP Scope

- [ ] Single checklist screen  
- [ ] Instant habit logging (> 90% in 2s)  
- [ ] Notifications with "don't ping when active" logic  
- [ ] Streak tracking with flexible skips  
- [ ] Local storage + iCloud sync  
- [ ] Elegant animations + light/dark theming  
- [ ] Data export (JSON/CSV)

---

## 7. Future Roadmap

- Optional: **Widgets** and **Apple Watch complications**  
- Support **multiple habits per day**  
- **Customization themes** subtly, preserving minimalist ethos

---

## 8. Success Metrics

- Daily Active Users (DAU) > 5,000 within 3 months  
- Average Open-to-log time < 2 seconds  
- Crash-free sessions ≥ 99.9%  
- ≥ 4.8 App Store rating with positive comments on speed & simplicity

---

## 9. Implementation Notes (for Claude-code AI)

- Write Swift modules using `apple-doc-mcp` code annotations for auto-generated API docs  
- Use Xcode MCP templates for consistent view tooling and CI integration  
- Prioritize TDD: write SwiftUI unit tests for animations, logging logic, and notifications  
- Automate localization-ready architecture for text strings

---

---

**Summary**:  
LightningHabit combines **Tinder‑style swipe UX** with **ultra‑minimalist** one‑screen design, **super‑fast** habit logging, and clean visual feedback. This yields a habit app experience that is **blazingly quick, intuitively obvious, and delightfully satisfying**—all while keeping the interface razor‑thin and user‑focused.

**End of PRD**  

_Crafted to guide the Claude-code AI assistant in generating code components, documentation, and UI layouts based on this product vision._