// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		88F5FD842E306BC60079ED47 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 88F5FD6C2E306BC50079ED47 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 88F5FD732E306BC50079ED47;
			remoteInfo = app;
		};
		88F5FD8E2E306BC60079ED47 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 88F5FD6C2E306BC50079ED47 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 88F5FD732E306BC50079ED47;
			remoteInfo = app;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		88F5FD742E306BC50079ED47 /* SwiftHabits.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftHabits.app; sourceTree = BUILT_PRODUCTS_DIR; };
		88F5FD832E306BC60079ED47 /* SwiftHabitsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SwiftHabitsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		88F5FD8D2E306BC60079ED47 /* SwiftHabitsUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SwiftHabitsUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		88F5FD762E306BC50079ED47 /* app */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = app;
			sourceTree = "<group>";
		};
		88F5FD862E306BC60079ED47 /* appTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = appTests;
			sourceTree = "<group>";
		};
		88F5FD902E306BC60079ED47 /* appUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = appUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		88F5FD712E306BC50079ED47 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F5FD802E306BC60079ED47 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F5FD8A2E306BC60079ED47 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		88F5FD6B2E306BC50079ED47 = {
			isa = PBXGroup;
			children = (
				88F5FD762E306BC50079ED47 /* app */,
				88F5FD862E306BC60079ED47 /* appTests */,
				88F5FD902E306BC60079ED47 /* appUITests */,
				88F5FD752E306BC50079ED47 /* Products */,
			);
			sourceTree = "<group>";
		};
		88F5FD752E306BC50079ED47 /* Products */ = {
			isa = PBXGroup;
			children = (
				88F5FD742E306BC50079ED47 /* SwiftHabits.app */,
				88F5FD832E306BC60079ED47 /* SwiftHabitsTests.xctest */,
				88F5FD8D2E306BC60079ED47 /* SwiftHabitsUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		88F5FD732E306BC50079ED47 /* SwiftHabits */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88F5FD972E306BC60079ED47 /* Build configuration list for PBXNativeTarget "SwiftHabits" */;
			buildPhases = (
				88F5FD702E306BC50079ED47 /* Sources */,
				88F5FD712E306BC50079ED47 /* Frameworks */,
				88F5FD722E306BC50079ED47 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				88F5FD762E306BC50079ED47 /* app */,
			);
			name = SwiftHabits;
			packageProductDependencies = (
			);
			productName = app;
			productReference = 88F5FD742E306BC50079ED47 /* SwiftHabits.app */;
			productType = "com.apple.product-type.application";
		};
		88F5FD822E306BC60079ED47 /* SwiftHabitsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88F5FD9A2E306BC60079ED47 /* Build configuration list for PBXNativeTarget "SwiftHabitsTests" */;
			buildPhases = (
				88F5FD7F2E306BC60079ED47 /* Sources */,
				88F5FD802E306BC60079ED47 /* Frameworks */,
				88F5FD812E306BC60079ED47 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				88F5FD852E306BC60079ED47 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				88F5FD862E306BC60079ED47 /* appTests */,
			);
			name = SwiftHabitsTests;
			packageProductDependencies = (
			);
			productName = appTests;
			productReference = 88F5FD832E306BC60079ED47 /* SwiftHabitsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		88F5FD8C2E306BC60079ED47 /* SwiftHabitsUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88F5FD9D2E306BC60079ED47 /* Build configuration list for PBXNativeTarget "SwiftHabitsUITests" */;
			buildPhases = (
				88F5FD892E306BC60079ED47 /* Sources */,
				88F5FD8A2E306BC60079ED47 /* Frameworks */,
				88F5FD8B2E306BC60079ED47 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				88F5FD8F2E306BC60079ED47 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				88F5FD902E306BC60079ED47 /* appUITests */,
			);
			name = SwiftHabitsUITests;
			packageProductDependencies = (
			);
			productName = appUITests;
			productReference = 88F5FD8D2E306BC60079ED47 /* SwiftHabitsUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		88F5FD6C2E306BC50079ED47 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					88F5FD732E306BC50079ED47 = {
						CreatedOnToolsVersion = 16.4;
					};
					88F5FD822E306BC60079ED47 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 88F5FD732E306BC50079ED47;
					};
					88F5FD8C2E306BC60079ED47 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 88F5FD732E306BC50079ED47;
					};
				};
			};
			buildConfigurationList = 88F5FD6F2E306BC50079ED47 /* Build configuration list for PBXProject "SwiftHabits" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				hu,
			);
			mainGroup = 88F5FD6B2E306BC50079ED47;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 88F5FD752E306BC50079ED47 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				88F5FD732E306BC50079ED47 /* SwiftHabits */,
				88F5FD822E306BC60079ED47 /* SwiftHabitsTests */,
				88F5FD8C2E306BC60079ED47 /* SwiftHabitsUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		88F5FD722E306BC50079ED47 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F5FD812E306BC60079ED47 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F5FD8B2E306BC60079ED47 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		88F5FD702E306BC50079ED47 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F5FD7F2E306BC60079ED47 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F5FD892E306BC60079ED47 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		88F5FD852E306BC60079ED47 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 88F5FD732E306BC50079ED47 /* SwiftHabits */;
			targetProxy = 88F5FD842E306BC60079ED47 /* PBXContainerItemProxy */;
		};
		88F5FD8F2E306BC60079ED47 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 88F5FD732E306BC50079ED47 /* SwiftHabits */;
			targetProxy = 88F5FD8E2E306BC60079ED47 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		88F5FD952E306BC60079ED47 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		88F5FD962E306BC60079ED47 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		88F5FD982E306BC60079ED47 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RD7CA3JP95;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = SwiftHabits;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lightninghabit.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		88F5FD992E306BC60079ED47 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RD7CA3JP95;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = SwiftHabits;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lightninghabit.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		88F5FD9B2E306BC60079ED47 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lightninghabit.app.tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SwiftHabits.SwiftHabits/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SwiftHabits";
			};
			name = Debug;
		};
		88F5FD9C2E306BC60079ED47 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lightninghabit.app.tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SwiftHabits.SwiftHabits/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SwiftHabits";
			};
			name = Release;
		};
		88F5FD9E2E306BC60079ED47 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lightninghabit.app.uitests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = app;
			};
			name = Debug;
		};
		88F5FD9F2E306BC60079ED47 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lightninghabit.app.uitests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = app;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		88F5FD6F2E306BC50079ED47 /* Build configuration list for PBXProject "SwiftHabits" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88F5FD952E306BC60079ED47 /* Debug */,
				88F5FD962E306BC60079ED47 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88F5FD972E306BC60079ED47 /* Build configuration list for PBXNativeTarget "SwiftHabits" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88F5FD982E306BC60079ED47 /* Debug */,
				88F5FD992E306BC60079ED47 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88F5FD9A2E306BC60079ED47 /* Build configuration list for PBXNativeTarget "SwiftHabitsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88F5FD9B2E306BC60079ED47 /* Debug */,
				88F5FD9C2E306BC60079ED47 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88F5FD9D2E306BC60079ED47 /* Build configuration list for PBXNativeTarget "SwiftHabitsUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88F5FD9E2E306BC60079ED47 /* Debug */,
				88F5FD9F2E306BC60079ED47 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 88F5FD6C2E306BC50079ED47 /* Project object */;
}
