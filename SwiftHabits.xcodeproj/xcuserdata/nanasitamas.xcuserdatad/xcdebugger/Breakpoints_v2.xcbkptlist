<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "2FB3F6A4-32AA-4F76-9860-3C1F3880558E"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B787BD82-23F5-42C0-A888-9A2D78260A62"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "app/Views/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "18"
            endingLineNumber = "18"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
